<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'total_qty' => $this->total_qty,
            'total_products' => $this->total_products,
            'coupon_amount' => $this->coupon_amount,
            'vat_amount' => $this->vat_amount,
            'delivery' => $this->deliver_price,
            'final_total' => $this->final_total,
            'coupon' => $this->when($this->coupon_id, [
                'id' => $this->coupon_id,
                'coupon_num' => $this->coupon_num,
                'type' => $this->coupon_type,
                'value' => $this->coupon_value,
                'discount_amount' => $this->coupon_amount,
            ]),
            'items' => CartItemResource::collection($this->whenLoaded('items')),
        ];
    }
}
