<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'discount_price' => $this->discount_price,
            'stock' => $this->stock,
            'image' => $this->image_url,
            'is_active' => $this->is_active,
            'category_id' => $this->category_id,
            'buy_times' => $this->buy_count,
            'rate_count' => $this->rates_count ?? 0,
            'rate_avg' => isset($this->avg_rate) ? round($this->avg_rate, 2) : 0,

            'category_name' => $this->category ? $this->category->name : null,
            'is_favorite' => $this->is_favorite, // Returns false for guest users, true/false for authenticated users
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
