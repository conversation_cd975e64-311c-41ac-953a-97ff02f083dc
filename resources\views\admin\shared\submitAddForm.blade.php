<script>
  $(document).ready(function() {
    $(document).on('submit', '.store', function(e) {
      e.preventDefault();

      var url = $(this).attr('action');

      $.ajax({
        url: url,
        method: 'post',
        data: new FormData($(this)[0]),
        dataType: 'json',
        processData: false,
        contentType: false,

        beforeSend: function() {
          $(".submit_button").html(
            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>'
          ).attr('disable', true);
        },

        success: function(response) {
          $(".text-danger").remove();
          $('.store input, .store textarea, .store select').removeClass('border-danger');
          $(".submit_button").html("{{ __('admin.add') }}").attr('disable', false);

          Swal.fire({
            position: 'top-start',
            icon: 'success',
            title: '{{ __('admin.added_successfully') }}',
            showConfirmButton: false,
            timer: 1500,
            confirmButtonClass: 'btn btn-primary',
            buttonsStyling: false,
          });

          if (response.url) {
            setTimeout(function() {
              window.location.replace(response.url);
            }, 1000);
          }
        },

        error: function(xhr) {
          $(".submit_button").html("{{ __('admin.add') }}").attr('disable', false);
          $(".text-danger").remove();
          $('.store input, .store textarea, .store select').removeClass('border-danger');

          let errorMessages = '';

          $.each(xhr.responseJSON.errors, function(key, value) {
            errorMessages += '- ' + value[0] + '\n';

            if (key.indexOf(".") >= 0) {
              var split = key.split('.');
              key = split[0] + '\\[' + split[1] + '\\]';
            }

            // عرض الرسالة أسفل الحقل
            $('.store .error.' + key).append(`<span class="mt-5 text-danger">${value}</span>`);
            $('.store input[name^="' + key + '"]').addClass('border-danger').after(`<span class="mt-5 text-danger">${value}</span>`);
            $('.store textarea[name^="' + key + '"]').addClass('border-danger').after(`<span class="mt-5 text-danger">${value}</span>`);
            $('.store select[name^="' + key + '"]').addClass('border-danger').after(`<span class="mt-5 text-danger">${value}</span>`);
          });

          Swal.fire({
            icon: 'error',
            title: 'حدثت أخطاء في البيانات',
            text: errorMessages,
            confirmButtonText: 'موافق'
          });

          console.log('Current input values:', $('.store').serializeArray());
        },
      });
    });
  });
</script>
