<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\Product;
use App\Models\User;
use App\Models\Coupon;
use Carbon\Carbon;

class CartService
{
    /**
     * Get the user's cart, create one if it doesn't exist
     *
     * @param User $user
     * @return Cart
     */
    public function getCart(User $user)
    {
        $cart = $user->cart;

        if (!$cart) {
            $cart = $user->cart()->create([
                'total_qty' => 0,
                'total_products' => 0,
                'final_total' => 0,
            ]);
        }

        return $cart;
    }

    /**
     * Add a product to the cart
     *
     * @param User $user
     * @param array $data
     * @return Cart
     * @throws \Exception
     */
    public function addToCart(User $user, array $data)
    {
        $cart = $this->getCart($user);

        $product = Product::findOrFail($data['product_id']);

        // Check if product is active
        if (!$product->is_active) {
            throw new \Exception(__('apis.product_not_available'));
        }

        // Check if product has enough stock
        if ($product->stock < $data['quantity']) {
            throw new \Exception(__('apis.insufficient_stock'));
        }

        // Check if product already exists in cart
        $cartItem = $cart->items()->where('product_id', $product->id)->first();

        if ($cartItem) {
            // Update quantity if product already exists in cart
            $cartItem->update([
                'quantity' => $data['quantity'],
                'price' => $product->price,
                'discount_price' => $product->discount_price,
                'total' => $product->discount_price ? $product->discount_price * $data['quantity'] : $product->price * $data['quantity'],
            ]);
        } else {
            // Add new product to cart
            $cart->items()->create([
                'product_id' => $product->id,
                'quantity' => $data['quantity'],
                'price' => $product->price,
                'discount_price' => $product->discount_price,
                'total' => $product->discount_price ? $product->discount_price * $data['quantity'] : $product->price * $data['quantity'],
            ]);
        }

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Update a cart item
     *
     * @param User $user
     * @param array $data
     * @return Cart
     * @throws \Exception
     */
    public function updateCartItem(User $user, array $data)
    {
        $cart = $this->getCart($user);

        $cartItem = $cart->items()->findOrFail($data['cart_item_id']);
        $product = $cartItem->product;

        // Check if product is active
        if (!$product->is_active) {
            throw new \Exception(__('apis.product_not_available'));
        }

        // Check if product has enough stock
        if ($product->stock < $data['quantity']) {
            throw new \Exception(__('apis.insufficient_stock'));
        }

        // Update cart item
        $cartItem->update([
            'quantity' => $data['quantity'],
            'total' => $product->discount_price ? $product->discount_price * $data['quantity'] : $product->price * $data['quantity'],
        ]);

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Remove a product from the cart
     *
     * @param User $user
     * @param int $cartItemId
     * @return Cart
     * @throws \Exception
     */
    public function removeFromCart(User $user, int $cartItemId)
    {
        $cart = $this->getCart($user);

        $cartItem = $cart->items()->findOrFail($cartItemId);
        $cartItem->delete();

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Clear the cart
     *
     * @param User $user
     * @return Cart
     * @throws \Exception
     */
    public function clearCart(User $user)
    {
        $cart = $this->getCart($user);

        $cart->items()->delete();

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart;
    }

    /**
     * Apply a coupon to the cart
     *
     * @param User $user
     * @param string $couponCode
     * @return Cart
     * @throws \Exception
     */
    public function applyCoupon(User $user, string $couponCode)
    {
        $cart = $this->getCart($user);

        // Find the coupon
        $coupon = Coupon::where('coupon_num', $couponCode)->first();

        if (!$coupon) {
            throw new \Exception(__('apis.coupon_not_found'));
        }

        // Validate coupon
        $this->validateCoupon($coupon);

        // Calculate discount amount
        $discountAmount = $this->calculateCouponDiscount($coupon, $cart->total_products);

        // Apply coupon to cart
        $cart->update([
            'coupon_id' => $coupon->id,
            'coupon_num' => $coupon->coupon_num,
            'coupon_type' => $coupon->type,
            'coupon_value' => $coupon->discount,
            'coupon_amount' => $discountAmount,
        ]);

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product', 'coupon');
    }

    /**
     * Remove coupon from the cart
     *
     * @param User $user
     * @return Cart
     * @throws \Exception
     */
    public function removeCoupon(User $user)
    {
        $cart = $this->getCart($user);

        // Remove coupon from cart
        $cart->update([
            'coupon_id' => null,
            'coupon_num' => null,
            'coupon_type' => null,
            'coupon_value' => null,
            'coupon_amount' => 0,
        ]);

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Validate coupon availability and conditions
     *
     * @param Coupon $coupon
     * @return void
     * @throws \Exception
     */
    private function validateCoupon(Coupon $coupon)
    {
        // Check if coupon is available
        if ($coupon->status !== 'available') {
            throw new \Exception(__('apis.coupon_not_available'));
        }

        // Check if coupon has not expired
        if ($coupon->expire_date && Carbon::parse($coupon->expire_date)->isPast()) {
            throw new \Exception(__('apis.coupon_expired'));
        }

        // Check if coupon has not started yet
        if ($coupon->start_date && Carbon::parse($coupon->start_date)->isFuture()) {
            throw new \Exception(__('apis.coupon_not_started'));
        }

        // Check if coupon usage limit has not been reached
        if ($coupon->use_times >= $coupon->max_use) {
            throw new \Exception(__('apis.coupon_usage_limit_reached'));
        }
    }

    /**
     * Calculate the discount amount for a coupon
     *
     * @param Coupon $coupon
     * @param float $totalProducts
     * @return float
     */
    private function calculateCouponDiscount(Coupon $coupon, float $totalProducts)
    {
        if ($coupon->type === 'ratio') {
            // Percentage discount
            $discountAmount = $totalProducts * ($coupon->discount / 100);

            // Apply max discount limit if set
            if ($coupon->max_discount && $discountAmount > $coupon->max_discount) {
                $discountAmount = $coupon->max_discount;
            }
        } else {
            // Fixed amount discount
            $discountAmount = $coupon->discount;

            // Don't allow discount to exceed total products
            if ($discountAmount > $totalProducts) {
                $discountAmount = $totalProducts;
            }
        }

        return $discountAmount;
    }

    /**
     * Update cart totals
     *
     * @param Cart $cart
     * @return void
     */
    private function updateCartTotals(Cart $cart)
    {
        // Calculate total products and quantity
        $totalProducts = $cart->items->sum('total');
        $totalQty = $cart->items->sum('quantity');

        // Get VAT and delivery from settings
        $settings = \App\Services\SettingService::appInformations(\App\Models\SiteSetting::pluck('value', 'key'));
        $vatPercentage = (float)($settings['vat_amount'] ?? 15);
        $deliveryAmount = (float)($settings['delivery'] ?? 0);

        // Get coupon discount amount
        $couponAmount = $cart->coupon_amount ?? 0;

        // Apply coupon discount to total products
        $totalAfterCoupon = $totalProducts - $couponAmount;

        // Ensure total doesn't go below zero
        if ($totalAfterCoupon < 0) {
            $totalAfterCoupon = 0;
        }

        // Calculate VAT amount on total after coupon discount
        $vatAmount = $totalAfterCoupon * ($vatPercentage / 100);

        // Calculate final total
        $finalTotal = $totalAfterCoupon + $vatAmount + $deliveryAmount;

        // Update cart
        $cart->update([
            'total_qty' => $totalQty,
            'total_products' => $totalProducts,
            'final_total' => $finalTotal,
            'vat_amount' => $vatAmount,
            'deliver_price' => $deliveryAmount,
        ]);
    }
}
