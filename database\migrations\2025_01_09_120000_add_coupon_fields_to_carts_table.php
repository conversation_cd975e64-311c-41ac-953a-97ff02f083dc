<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->foreignId('coupon_id')->nullable()->constrained()->onDelete('set null');
            $table->string('coupon_num')->nullable();
            $table->string('coupon_type')->nullable(); // 'ratio' or 'number'
            $table->decimal('coupon_value', 9, 2)->nullable(); // The discount value
            $table->decimal('coupon_amount', 9, 2)->default(0); // The actual discount amount applied
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->dropForeign(['coupon_id']);
            $table->dropColumn(['coupon_id', 'coupon_num', 'coupon_type', 'coupon_value', 'coupon_amount']);
        });
    }
};
