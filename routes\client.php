<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Client\CartController;
use App\Http\Controllers\Api\Client\GiftController;
use App\Http\Controllers\Api\Client\OrderController;
use App\Http\Controllers\Api\Client\AddressController;
use App\Http\Controllers\Api\Client\ProductController;
use App\Http\Controllers\Api\Client\CategoryController;
use App\Http\Controllers\Api\Client\FavoriteController;
use App\Http\Controllers\Api\Client\ContactUsController;
use App\Http\Controllers\Api\Client\OrderRateController;
use App\Http\Controllers\Api\Client\OrderReportController;
use App\Http\Controllers\Api\Client\ProductRateController;

Route::group([
    'namespace'  => 'Api',
    'middleware' => ['api-cors', 'api-lang'],
    'prefix'     => 'client',
], function () {

    // Routes that work for both guest and authenticated users
    Route::group(['middleware' => ['OptionalSanctumMiddleware']], function () {
        /***************************** CategoryController start *****************************/
        Route::get('categories', [CategoryController::class, 'index'])->name('client.categories.index');
        Route::get('categories/{id}', [CategoryController::class, 'show'])->name('client.categories.show');
        /***************************** CategoryController End *****************************/

        /***************************** ProductController start *****************************/
        Route::get('products', [ProductController::class, 'index'])->name('client.products.index');
        Route::get('products/{id}', [ProductController::class, 'show'])->name('client.products.show');
        Route::get('products-by-category', [ProductController::class, 'groupedByCategory'])->name('client.products.grouped-by-category');
        /***************************** ProductController End *****************************/
    });

    Route::group(['middleware' => ['auth:sanctum', 'is-active', 'check-client-type']], function () {
        /***************************** AddressController start *****************************/
        Route::apiResource('addresses', AddressController::class, [
            'names' => [
                'index' => 'client.addresses.index',
                'store' => 'client.addresses.store',
                'show' => 'client.addresses.show',
                'update' => 'client.addresses.update',
                'destroy' => 'client.addresses.destroy',
            ]
        ]);

        Route::post('favorite/toggle', [FavoriteController::class, 'toggle'])->name('client.favorite.toggle');
        Route::get('favorite', [FavoriteController::class, 'index'])->name('client.favorite.index');
     
        /***************************** AddressController end *****************************/

        /***************************** CartController start *****************************/
        Route::get('cart', [CartController::class, 'getCart'])->name('client.cart.get');
        Route::post('cart/add', [CartController::class, 'addToCart'])->name('client.cart.add');
        Route::put('cart/update', [CartController::class, 'updateCartItem'])->name('client.cart.update');
        Route::delete('cart/remove', [CartController::class, 'removeFromCart'])->name('client.cart.remove');
        Route::delete('cart/clear', [CartController::class, 'clearCart'])->name('client.cart.clear');
        /***************************** CartController end *****************************/

        /***************************** OrderController start *****************************/
        Route::get('orders', [OrderController::class, 'getOrders'])->name('client.orders.list');
        Route::get('orders/{id}', [OrderController::class, 'getOrder'])->name('client.orders.show');
        Route::post('orders/create', [OrderController::class, 'createOrder'])->name('client.orders.create');
        Route::post('orders/cancel', [OrderController::class, 'cancelOrder'])->name('client.orders.cancel');
        /***************************** OrderController end *****************************/

        /***************************** OrderRateController start *****************************/
        Route::post('orders/rate', [OrderRateController::class, 'store'])->name('client.orders.store');
        /***************************** OrderRateController end *****************************/
        Route::post('products/rate', [ProductRateController::class, 'store'])->name('client.products.store');

        /***************************** OrderReportController start *****************************/
        Route::post('orders/report', [OrderReportController::class, 'store'])->name('client.orders.store');
        /***************************** OrderReportController end *****************************/

        /***************************** GiftController start *****************************/
        Route::get('gifts', [GiftController::class, 'index'])->name('client.gifts.index');
        Route::get('gifts/available', [GiftController::class, 'available'])->name('client.gifts.available');
        Route::get('gifts/pending', [GiftController::class, 'pending'])->name('client.gifts.pending');
        Route::get('gifts/stats', [GiftController::class, 'stats'])->name('client.gifts.stats');
        /***************************** GiftController end *****************************/
    });
});
