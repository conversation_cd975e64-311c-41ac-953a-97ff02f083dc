<?php

namespace App\Http\Controllers\Api\Client;

use App\Services\Responder;
use App\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use App\Services\ProductRateService;
use App\Http\Requests\Api\Order\RateOrderRequest;
use App\Http\Requests\Api\Order\RateProductRequest;
use App\Http\Resources\Api\Order\OrderRateResource;

class ProductRateController extends Controller
{
    use ResponseTrait;

    /**
     * @var ProductRateService
     */
    protected $productRateService;

    /**
     * OrderRateController constructor.
     *
     * @param productRateService $productRateService
     */
    public function __construct(ProductRateService $productRateService)
    {
        $this->productRateService = $productRateService;
    }

    /**
     * Rate an order
     *
     * @param RateProductRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
  public function store(RateProductRequest $request)
{
    try {
        $user = auth()->user();
        $rating = $this->productRateService->rateProduct($user, $request->validated());

        return Responder::success(
            null,
            ['message' => __('apis.success')]
        );
    } catch (\Exception $e) {
        \Log::error('❌ خطأ أثناء تقييم الطلب: ' . $e->getMessage());
        return Responder::error($e->getMessage(), [], 422);
    }
}



   
}
