<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\OrderRate;
use App\Enums\OrderStatus;
use App\Models\ProductRate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductRateService
{
    /**
     * Rate an order
     *
     * @param User $user
     * @param array $data
     * @return OrderRate
     * @throws \Exception
     */
    public function rateProduct(User $user, array $data): ProductRate
    {
        DB::beginTransaction();

        try {
            // Get the order
            $product = Product::findOrFail($data['product_id']);
            // Create the rating
            $rating = ProductRate::create([
                'product_id' => $product->id,
                'rate' => $data['rate'],
                'user_id' => $user->id,
                'notes' => $data['notes'] ?? null,
            ]);

            DB::commit();
            return $rating;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order rating failed', [
                'error' => $e->getMessage(),
                'order_id' => $data['order_id'] ?? null,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }


}
