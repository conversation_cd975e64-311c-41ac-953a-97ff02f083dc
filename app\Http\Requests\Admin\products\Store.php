<?php

namespace App\Http\Requests\Admin\products;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $default_lang = config('app.locale');

        return [
           'title'                 => 'required|array',
"title.{$default_lang}"  => 'required|string|min:10|max:191',
'title.*'               => 'required|string|min:10|max:191',

'description'           => 'required|array',
"description.{$default_lang}" => 'required|string|min:30',
'description.*'         => 'required|string|min:30',

            'price'                 => 'required|numeric|min:0',
            'discount_price'        => 'nullable|numeric|min:0',
            'stock'                 => 'required|integer|min:0',
            'image'                 => 'required|image',
            'is_active'             => 'nullable|boolean',
            'category_id'           => 'required|exists:categories,id',
        ];
    }


    public function messages()
{
    return [
        "title.en.required" => "العنوان باللغة الإنجليزية مطلوب",
        "title.en.min" => "العنوان باللغة الإنجليزية يجب ألا يقل عن 10 حروف",
        "title.en.max" => "العنوان باللغة الإنجليزية يجب ألا يزيد عن 191 حرفًا",

        "description.en.required" => "الوصف باللغة الإنجليزية مطلوب",
        "description.en.min" => "الوصف باللغة الإنجليزية يجب ألا يقل عن 30 حرفًا",

        'title.*.required' => 'العنوان مطلوب لجميع اللغات',
        'title.*.min' => 'العنوان يجب ألا يقل عن 10 حروف',
        'title.*.max' => 'العنوان يجب ألا يزيد عن 191 حرفًا',

        'description.*.required' => 'الوصف مطلوب لجميع اللغات',
        'description.*.min' => 'الوصف يجب ألا يقل عن 30 حرفًا',

        'price.required' => 'السعر مطلوب',
        'price.numeric' => 'السعر يجب أن يكون رقمًا',
        'price.min' => 'السعر لا يمكن أن يكون أقل من 0',

        'discount_price.numeric' => 'سعر الخصم يجب أن يكون رقمًا',
        'discount_price.min' => 'سعر الخصم لا يمكن أن يكون أقل من 0',

        'stock.required' => 'المخزون مطلوب',
        'stock.integer' => 'المخزون يجب أن يكون عددًا صحيحًا',
        'stock.min' => 'المخزون لا يمكن أن يكون أقل من 0',

        'image.required' => 'الصورة مطلوبة',
        'image.image' => 'الملف يجب أن يكون صورة',

        'category_id.required' => 'اختيار القسم مطلوب',
        'category_id.exists' => 'القسم المختار غير موجود',
    ];
}
}
