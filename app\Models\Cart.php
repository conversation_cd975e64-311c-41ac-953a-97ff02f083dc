<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    protected $fillable = [
        'user_id',
        'total_qty',
        'total_products',
        'final_total',
        'deliver_price',
        'vat_amount',
        'coupon_id',
        'coupon_num',
        'coupon_type',
        'coupon_value',
        'coupon_amount'
    ];

    protected $casts = [
        'total_qty' => 'integer',
        'total_products' => 'decimal:2',
        'final_total' => 'decimal:2',
        'deliver_price'=> 'decimal:2',
        'vat_amount'=> 'decimal:2',
        'coupon_value' => 'decimal:2',
        'coupon_amount' => 'decimal:2'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(CartItem::class);
    }

    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }
}
