<?php

namespace App\Http\Resources\Api\Notifications;

use App\Models\OrderReport;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\OrderRate;

class NotificationsResource extends JsonResource
{
    public function toArray($request)
    {
        $locale = app()->getLocale();
        $type = $this->data['type'] ?? null;

        // إشعار تقييم الطلب
        if ($type === 'order_rated') {
            $orderId = $this->data['order_id'] ?? null;
            $rate = OrderRate::where('order_id', $orderId)->first();

            return [
                'id'         => $this->id,
                'order_id'   => $orderId,
                'type'       => $type,
                'title'      => __('notification.order_rated_title', ['order_id' => $orderId]),
                'body'       => $rate
                    ? __('notification.order_rated_body', ['rate' => $rate->rate, 'note' => $rate->note ?? __('notification.no_note')])
                    : __('notification.no_rating'),
                'created_at' => $this->created_at->diffForHumans(),
            ];
        }

        // إشعار بلاغ الطلب
        if ($type === 'order_reported') {
            $orderId = $this->data['order_id'] ?? null;
            $report = OrderReport::where('order_id', $orderId)->latest()->first();

            return [
                'id'         => $this->id,
                'order_id'   => $orderId,
                'type'       => $type,
                'title'      => __('notification.order_reported_title', ['order_id' => $orderId]),
                'body'       => $report?->note ?? __('notification.no_report_note'),
                'created_at' => $this->created_at->diffForHumans(),
            ];
        }

        // باقي أنواع الإشعارات
        $title = $this->title;
        $body = $this->body;

        if (is_array($title)) {
            $title = $title[$locale] ?? ($title['ar'] ?? reset($title));
        }

        if (is_array($body)) {
            $body = $body[$locale] ?? ($body['ar'] ?? reset($body));
        }

        return [
            'id'         => $this->id,
            'order_id'   => $this->data['order_id'] ?? null,
            'type'       => $type,
            'title'      => $title ?? $this->data['title'] ?? '',
            'body'       => $body ?? $this->data['body'] ?? '',
            'created_at' => $this->created_at->diffForHumans(),
        ];
    }
}
