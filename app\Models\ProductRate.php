<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductRate extends Model
{
    protected $fillable = [
        'user_id',
        'product_id',
        'rate',
        'notes',

    ];

    protected $casts = [
        'rate' => 'decimal:2',
    ];

    /**
     * Get the order that owns the rating
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
