<?php
namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class OrderRatedNotification extends Notification
{
    use Queueable;

    protected $order;

    public function __construct($order)
    {
        $this->order = $order;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

public function toArray($notifiable)
{
    return [
        'order_id' => $this->order->id,
        'type'     => 'order_rated',
        'rate'     => $this->order->rate->rate ?? null,
        'note'     => $this->order->rate->note ?? null,
    ];
}





}
