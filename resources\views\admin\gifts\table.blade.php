<div class="position-relative">
    {{-- table loader  --}}
    <div class="table_loader" style="display: none;">
        {{__('admin.loading')}}
    </div>
    {{-- table loader  --}}

    {{-- table content --}}
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>{{__('admin.date')}}</th>
                <th>{{__('admin.orders_count')}}</th>
                <th>{{__('admin.month')}}</th>
                <th>{{__('admin.coupon')}}</th>
                <th>{{__('admin.status')}}</th>
                <th>{{__('admin.control')}}</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($gifts as $gift)
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                        <input type="checkbox" class="checkSingle" id="{{ $gift->id }}">
                        <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $gift->created_at->format('d/m/Y') }}</td>
                    <td>{{ $gift->orders_count }}</td>
                    <td>{{ $gift->month->format('Y-m') }}</td>
                    <td>{{ $gift->coupon ? $gift->coupon->coupon_num : '-' }}</td>
                   <td>
                        {!! toggleBooleanView($gift , route('admin.model.active' , ['model' =>'Gift' , 'id' => $gift->id , 'action' => 'is_active'])) !!}
                    </td>

                    <td class="product-action">
                        <span class="text-primary"><a href="{{ route('admin.gifts.show', ['id' => $gift->id]) }}" class="btn btn-warning btn-sm"><i class="feather icon-eye"></i> {{ __('admin.show') }}</a></span>
                        <span class="action-edit text-primary"><a href="{{ route('admin.gifts.edit', ['id' => $gift->id]) }}" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i>{{ __('admin.edit') }}</a></span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="{{ url('admin/gifts/' . $gift->id) }}"><i class="feather icon-trash"></i>{{ __('admin.delete') }}</span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}
    {{-- no data found div --}}
    @if ($gifts->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{asset('admin/app-assets/images/pages/404.png')}}" alt="">
            <span class="mt-2" style="font-family: cairo">{{__('admin.there_are_no_matches_matching')}}</span>
        </div>
    @endif
    {{-- no data found div --}}

</div>
{{-- pagination  links div --}}
@if ($gifts->count() > 0 && $gifts instanceof \Illuminate\Pagination\AbstractPaginator )
    <div class="d-flex justify-content-center mt-3">
        {{$gifts->links()}}
    </div>
@endif
{{-- pagination  links div --}}

