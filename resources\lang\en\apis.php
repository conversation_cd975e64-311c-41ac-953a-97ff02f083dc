<?php

return [

  /*
  |--------------------------------------------------------------------------
  | Authentication Language Lines
  |--------------------------------------------------------------------------
  |
  | The following language lines are used during authentication for various
  | messages that we need to display to the user. You are free to modify
  | these language lines according to your application's requirements.
  |
   */

  'loggedOut'           => 'Log Out successfully',
  'AdminNotify'         => 'Admin Notify',
  'photoadadded'        => 'Photo Ad Added successfully',
  'passwordReset'       => 'Password Reset successfully',
  'newComment'          => 'New Comment',
  'adDeleted'           => 'Ad Deleted successfully',
  'reportAdded'         => 'Report Added successfully',
  'signed'              => 'sign in successfully',
  'added'               => 'Added successfully',
  'commentAdded'        => 'Comment Added successfully',
  'commentDeleted'      => 'Comment deleted successfully',
  'unauthorize'         => 'un authorize to process this action',
  'rated'               => 'Your Rate Added successfully',
  'fav'                 => 'Added to Favorite successfully',
  'unFav'               => 'Deleted from Favorite successfully',
  'openNotify'          => 'Notifications Open successfully',
  'closeNotify'         => 'Notifications Closed successfully',
  'transfered'          => 'Bank transfer sent successfully',
  'addAdded'            => 'ad Added successfully',
  'newMessage'          => 'You have new message from: attr',
  'messageSended'       => 'Message Sended successfully',
  'updated'             => 'updated successfully',
  'refreshed'           => 'Ad Refreshed successfully',
  'send_activated'      => 'Activation Code Send successfully',
  'phone_changed'       => 'Phone Changed successfully',
  'email_changed'       => 'Email Changed successfully',
  'success'             => 'success',
  'deleted'             => 'deleted',
  'not_avilable_coupon' => 'The coupon is not available for use at the moment',
   'coupon_end_at'      => 'coupon expired on  : date',
  'coupon_start_at'     => 'coupon start usage on  : date',
   'disc_amount'        => 'discount amount',
   'coupon_not_found'   => 'Coupon code not found',
   'coupon_not_available' => 'Coupon code is not available for use',
   'coupon_expired'     => 'Coupon code has expired',
   'coupon_not_started' => 'Coupon code has not started yet',
   'coupon_usage_limit_reached' => 'Coupon usage limit has been reached',
   'coupon_applied_successfully' => 'Coupon applied successfully',
   'coupon_removed_successfully' => 'Coupon removed successfully',
   'rs'                 => 'Saudi Riyals',
   'model_not_found'    => 'This item not found!',
   'route_not_found'    => 'This http url not exists!',
   'activeCode'          => 'your activation code: ',
   'removed'             => 'Removed successfully',
   'category_not_found'  => 'Category not found!',
   'product_not_found'   => 'Product not found!',
   'cart_is_empty'       => 'Cart is empty',
   'cart_empty'          => 'Your cart is empty. Please add products to your cart before creating an order.',
   'cart_not_found'      => 'Cart not found',
   'product_added_to_cart' => 'Product added to cart successfully',
   'cart_updated'        => 'Cart updated successfully',
   'product_removed_from_cart' => 'Product removed from cart successfully',
   'cart_cleared'        => 'Cart cleared successfully',
   'product_not_available' => 'Product is not available',
   'insufficient_stock'  => 'Insufficient stock',
   'order_created'       => 'Order created successfully',
   'order_canceled'      => 'Order canceled successfully',
   'closeOrder'          => 'Order completed successfully',
   'order_cannot_be_canceled' => 'This order cannot be canceled',
   'order_status_updated' => 'Order status updated successfully',
   'invalid_order_status' => 'Invalid order status',
   'cannot_use_cancel_status' => 'Please use the cancel order endpoint to cancel an order',
   'address_not_found'   => 'Address not found',
   'max_usa_coupon'      => 'This coupon has reached its maximum usage limit',
   'order_creation_failed' => 'Failed to create order. Please try again.',
    'order_not_found'     => 'Order not found',

    // Order Rating Messages
    'order_id_required' => 'Order ID is required',
    'rate_required' => 'Rating is required',
    'rate_must_be_numeric' => 'Rating must be a number',
    'rate_min_value' => 'Rating must be at least 1',
    'rate_max_value' => 'Rating cannot be more than 5',
    'note_max_length' => 'Note cannot be more than 1000 characters',
    'order_not_belongs_to_user' => 'This order does not belong to you',
    'order_must_be_delivered_to_rate' => 'Order must be delivered before it can be rated',
    'order_already_rated' => 'This order has already been rated',
    'order_not_rated' => 'This order has not been rated yet',
    'order_rated_successfully' => 'Order rated successfully',
    'order_rating_updated_successfully' => 'Order rating updated successfully',

    // Order Report Messages
    'report_note_required' => 'Report note is required',
    'report_note_must_be_string' => 'Report note must be text',
    'report_note_max_length' => 'Report note cannot be more than 1000 characters',
    'order_already_reported' => 'You have already reported this order',
    'report_not_belongs_to_user' => 'This report does not belong to you',
    'order_reported_successfully' => 'Order reported successfully',
//addresses_shown
  'addresses_shown' => 'Addresses shown successfully',

  'address_created' => 'Address created successfully',
  'address_updated' => 'Address updated successfully',
  'address_deleted' => 'Address deleted successfully',
  //addresses_unauthorized
  'addresses_unauthorized' => 'Unauthorized to access addresses',
  'not_found' => 'Not found',
  'addresses' => [
    'unauthorized' => 'Unauthorized to access this address',
    'not_found' => 'Address not found',
    'shown' => 'Address details shown successfully',
    'updated' => 'Address updated successfully',
    'deleted' => 'Address deleted successfully',
  ],

    // Gifts
    'gifts_retrieved_successfully' => 'Gifts retrieved successfully',
    'available_gifts_retrieved_successfully' => 'Available gifts retrieved successfully',
    'pending_gifts_retrieved_successfully' => 'Pending gifts retrieved successfully',
    'stats_retrieved_successfully' => 'Statistics retrieved successfully',
    'no_gifts_available' => 'No gifts available for this month',
    'unauthorized' => 'Unauthorized access',
    'something_went_wrong' => 'Something went wrong',
    'currency' => 'SAR',
    'delivery_reported_successfully'=> 'Order reported successfully',
    'unauthorized_action' => 'You are not allowed to modify this order',
    'reported_order_cannot_be_modified' => 'You cannot modify a reported order.',
'delivered_order_cannot_be_modified' => 'You cannot modify an order after it has been delivered.',

  ];
