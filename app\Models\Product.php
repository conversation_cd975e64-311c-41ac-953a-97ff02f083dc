<?php

namespace App\Models;

use App\Traits\UploadTrait;
use Illuminate\Support\Facades\Auth;
use Spatie\Translatable\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends BaseModel
{
    const IMAGEPATH = 'products' ;
    use HasTranslations, UploadTrait, HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'price',
        'discount_price',
        'stock',
        'image',
        'is_active',
        'category_id'
    ];

    public $translatable = ['title','description'];

    protected $casts = [
        'price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function cartItems()
    {
        return $this->hasMany(CartItem::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }



    /**
     * Check if the current authenticated user has favorited this product
     * Returns false for guest users, true/false for authenticated users
     *
     * @return bool
     */
    public function getIsFavoriteAttribute()
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return false;
        }

        // Get the current authenticated user ID
        $userId = Auth::id();
        // Check if this product is favorited by the current user
        return $this->favorites()->where('user_id', $userId)->exists();
    }

    /**
     * Get the product image URL with a fallback to default image
     *
     * @return string
     */
    public function getImageUrlAttribute()
    {
        if (isset($this->attributes['image']) && $this->attributes['image'] && $this->attributes['image'] != 'default.png') {
            return $this->getImage($this->attributes['image'], self::IMAGEPATH);
        }

        return $this->defaultImage(self::IMAGEPATH);
    }

    public function getBuyCountAttribute()
    {
        return $this->orderItems()->where('product_id', $this->id)->count();
    }

    public function rates()
    {
        return $this->hasMany(ProductRate::class);
    }

    /**
     * Get the count of rates for the product.
     *
     * @return int
     */
    public function getRatesCountAttribute()
    {
        return $this->rates()->count();
    }

    /**
     * Get the average rate for the product.
     *
     * @return float|null
     */
    public function getAvgRateAttribute()
    {
        return $this->rates()->avg('rate');
    }

}
